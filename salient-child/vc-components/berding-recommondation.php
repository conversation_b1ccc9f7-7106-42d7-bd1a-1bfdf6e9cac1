<?php
if ( ! class_exists( 'BerdingRecommendation' ) ) {

	class BerdingRecommendation extends WPBakeryShortCode {

		function __construct() {
			add_action( 'init', array( $this, 'create_shortcode' ), 999 );
			add_shortcode( 'berding_recommendation', array( $this, 'render_shortcode' ) );
		}

		public function create_shortcode() {
			// Stop all if VC is not enabled
			if ( !defined( 'WPB_VC_VERSION' ) ) {
				return;
			}

			// Map blockquote with vc_map()
			vc_map( array(
				'name'        => __('Berding Content Box', 'rr'),
				'base'        => 'berding_recommendation',
				'category'    => __( 'rr Modules', 'rr'),
				'params' => array(
					array(
						'type'        => 'textfield',
						'heading'     => __( 'Headline', 'rr' ),
						'param_name'  => 'headline',
						'value'       => __( 'Empfehlung', 'rr' ),
					),
					array(
						'type'        => 'textarea_html',
						'heading'     => __( 'Text', 'rr' ),
						'param_name'  => 'content',
						'value'       => __( 'Lorem ipsum et dolor...', 'rr' ),
					),
					array(
						'type'        => 'attach_image',
						'heading'     => __( 'Bild', 'rr' ),
						'param_name'  => 'image',
						'description' => __( 'Optionales Bild für die Empfehlung', 'rr' ),
					),
                    array(
                        'type'        => 'vc_link',
                        'heading'     => __( 'Button URL', 'my-text-domain' ),
                        'param_name'  => 'url',
                    ),
                    array(
                        'type' => 'checkbox',
                        'heading' => 'Center Content',
                        'param_name' => 'center_content',
                        'value' => array('Ja' => 'true'),
                        'description' => 'Zentriert den Inhalt',
                    ),
					array(
						'type'        => 'checkbox',
						'heading'     => __( 'Dunkler Hintergrund', 'rr' ),
						'param_name'  => 'dark_background',
						'value'       => array( __( 'Ja', 'rr' ) => 'yes' ),
						'description' => __( 'Aktiviert den blauen Verlaufshintergrund mit weißem Text.', 'rr' ),
					),
				),
			));
		}

		public function render_shortcode( $atts, $content = null ) {
			$atts = shortcode_atts(array(
				'headline'         => 'Empfehlung',
				'image'            => '',
                'url'              => '',
				'dark_background'  => '',
                'center_content'   => '',
			), $atts);

			$image_url = wp_get_attachment_image_url( $atts['image'], 'full' );
            $image_class = $image_url ? ' has-image' : '';

			$dark_class = ( $atts['dark_background'] === 'yes' ) ? ' dark-background' : '';

            $center_content_class = ( $atts['center_content'] === 'true' || $atts['center_content'] === 'yes' ) ? ' center-content' : '';

            // Link / Button
            $link = vc_build_link( $atts['url'] );
            $href = isset( $link['url'] ) ? esc_url( $link['url'] ) : '#';
            $target = isset( $link['target'] ) ? esc_attr( $link['target'] ) : '_self';
            $link_text = isset( $link['title'] ) ? esc_html( $link['title'] ) : 'Button';

            $button_classes = "custom-vc-button medium";
            $button_styles = [
                'light' => '--default-color: #ffffff; --text-color: #0f2851; --hover-color: #ea0000; --hover-text-color: #ffffff;',
                'dark'  => '--default-color: #0f2851; --text-color: #ffffff; --hover-color: #ea0000; --hover-text-color: #ffffff;',
            ];

            $button_style = '';
            if( $atts['dark_background'] === 'yes' ) {
                $button_classes .= ' dark';
                $button_style = $button_styles['light'];
            } else {
                $button_style = $button_styles['dark'];
                $button_classes .= ' light';
            }


			$output = '<div class="recommendation-wrapper' . esc_attr( $dark_class . $image_class . $center_content_class ) . ' ">';
                $output .= '<div class="vignetten-wrapper"></div>';
                $output .= '<div class="recommendation-box' . esc_attr( $dark_class ) . '">';
                    $output .= '<div class="text-content">';
                        $output .= '<h3>' . esc_html( $atts['headline'] ) . '</h3>';
                        $output .= '<p>' . wpb_js_remove_wpautop( $content, true ) . '</p>';
                        // $output .= '<p>$href: ' .  $href . '</p>';
                        // $output .= '<p>$target: ' .  $target . '</p>';
                        // $output .= '<p>$link_text: ' .  $link_text . '</p>';
                        if ( $href && $link_text ) {
                            $output .= '<a href="' . $href . '" target="' . $target . '" class="' . esc_attr( $button_classes ) . '" style="margin-top: 1rem; ' . esc_attr( $button_style ) . '">' . $link_text . '</a>';
                        }
                        // $output .= '<p>$center_content_class: ' .  $center_content_class . '</p>';
                        // $output .= '<p>$dark_class: ' .  $dark_class . '</p>';
                    $output .= '</div>';
                        if ( $image_url ) {
                        $output .= '<div class="image-content">';
                            $output .= '<img src="' . esc_url( $image_url ) . '" alt="">';
                            // $output .= '<p>' . $image_url . '</p>';
                        $output .= '</div>';
                        }
                $output .= '</div>';
			$output .= '</div>';

			$output .= '<style>
				.recommendation-wrapper {
					position: relative;
                    width: 100%;
				}
                body .row .recommendation-wrapper h3 {
                    padding: 0 0 1rem 0;
                    margin: 0;
                    font-size: 48px;
                }

				.recommendation-wrapper.dark-background {
					color: white;
					background: transparent radial-gradient(closest-side at 50% 50%, #535c74 0%, #162751 150%) 0% 0% no-repeat padding-box;
                    color: #fff;
				}
                .recommendation-wrapper.dark-background h3 {
                    color: #fff;
                }

                
				.recommendation-wrapper.dark .vignetten-wrapper {
					height: 100%;
					position: absolute;
					width: 100%;
					box-shadow: 0 0 83vw rgb(0 0 0 / 50%) inset;
					z-index: 0;
				}

                .recommendation-box {
                    max-width: 1425px;
                    padding: 3% 90px;
                    margin: auto;
                    align-items: center;
                }
                

                .recommendation-wrapper.has-image .recommendation-box {
                    display: grid;
                    grid-template-columns: 2fr 1fr;
                    gap: 2rem;
                }


                .recommendation-wrapper.center-content .recommendation-box {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    text-align: center;
                }
                .recommendation-wrapper.center-content .recommendation-box h3,
                .recommendation-wrapper.center-content .recommendation-box p {
                    text-align: center;
                }

                /* Fallback if button classes are missing */
                .custom-vc-button {
                    display: inline-block;
                    padding: 12px 40px;
                    border-radius: 0;
                    clip-path: polygon(50% 0%, 100% 0, 95% 50%, 90% 100%, 75% 100%, 50% 100%, 15% 100%, 0 100%, 5% 50%, 10% 0);
                    text-decoration: none;
                    font-weight: 600;
                    background-color: var(--default-color);
                    color: var(--text-color);
                    transition: background-color 0.3s ease, color 0.3s ease;
                }
                .custom-vc-button:hover {
                    background-color: var(--hover-color);
                    color: var(--hover-text-color);
                }
                .custom-vc-button.small {
                    font-size: 12px;
                    padding: 5px 10px;
                }
                .custom-vc-button.medium {
                    font-size: 16px;
                }
                .custom-vc-button.large {
                    font-size: 20px;
                    padding: 15px 30px;
                }

                .custom-vc-button.full-width {
                    display: block;
                    width: 100%;
                    text-align: center;
                }


                @media screen and (max-width: 999px) {
                    .recommendation-box {
                        max-width: 600px;
                    }

                    .recommendation-wrapper.has-image .recommendation-box {
                        grid-template-columns: 1fr !important;
                        padding: 4rem 0;
                    }
                    body .row .recommendation-wrapper h3 {
                        font-size: 25.5px;
                        line-height: 31.5px;
                    }
                }
                @media screen and (max-width: 690px) {
                    .recommendation-box {
                        max-width: 320px;
                    }
                }

                
			</style>';

			return $output;
		}
	}

	new BerdingRecommendation();
}
