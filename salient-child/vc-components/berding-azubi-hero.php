<?php
if (!class_exists('WPBakeryShortCode_BB_Job_Teaser')) {
    class WPBakeryShortCode_BB_Job_Teaser extends WPBakeryShortCode {
    }
}

function bb_job_teaser_shortcode($atts, $content = null) {
    $atts = shortcode_atts([
        'background_image'   => '',
        'intro_text'         => '',
        'job_title'          => '',
        'job_title_extra'    => '',
        'salary'             => '',
        'salary_extra'       => '',
        'requirement'        => '',
        'requirement_extra'  => '',
        'start_date'         => '',
        'start_date_extra'   => '',
        'duration'           => '',
        'duration_extra'     => ''
    ], $atts);

    $bg_image = wp_get_attachment_image_url($atts['background_image'], 'full');

    ob_start();
    ?>
    <div class="bb-job-teaser" style="background-image: url('<?php echo esc_url($bg_image); ?>');">
        <div class="bb-job-content">
            <h2 class="bb-job-intro"> <?php echo esc_html($atts['intro_text']); ?> </h2>
            <h1 class="bb-job-title"> <?php echo esc_html($atts['job_title']); ?> </h1>
            <?php if (!empty($atts['job_title_extra'])) : ?>
                <p class="bb-job-title-extra"><?php echo esc_html($atts['job_title_extra']); ?></p>
            <?php endif; ?>
            <div class="bb-job-info">
                <div class="bb-job-item">
                    <span>GEHALT</span>
                    <div>
                        <p><?php echo esc_html($atts['salary']); ?></p>
                        <?php if (!empty($atts['salary_extra'])) : ?>
                            <p class="extra"><?php echo esc_html($atts['salary_extra']); ?></p>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="bb-job-item">
                    <span>VORAUSSETZUNG</span>
                    <div>
                        <p><?php echo esc_html($atts['requirement']); ?></p>
                        <?php if (!empty($atts['requirement_extra'])) : ?>
                            <p class="extra"><?php echo esc_html($atts['requirement_extra']); ?></p>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="bb-job-item">
                    <span>BEGINN</span>
                    <div>
                        <p><?php echo esc_html($atts['start_date']); ?></p>
                        <?php if (!empty($atts['start_date_extra'])) : ?>
                            <p class="extra"><?php echo esc_html($atts['start_date_extra']); ?></p>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="bb-job-item">
                    <span>DAUER</span>
                    <div>
                        <p><?php echo esc_html($atts['duration']); ?></p>
                        <?php if (!empty($atts['duration_extra'])) : ?>
                            <p class="extra"><?php echo esc_html($atts['duration_extra']); ?></p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <style>
        .bb-job-teaser {
            position: relative;
            background-size: cover;
            background-position: center;
            padding: 50px;
            color: white;
            text-align: left;
            min-height: 66vh;
            background-position: center;
            display: flex;
            align-items: end;
            margin-bottom: 80px !important;
            text-shadow: 
        }
        .bb-job-content {
            width: 100%;
        }
        .bb-job-intro {
            font-size: 48px;
            font-weight: bold;
            color: #fff;
            margin-bottom: 0;
            text-shadow: 0 0 6px rgba(0, 0, 0, 0.2), 0 0 12px rgba(0, 0, 0, .1);
        }
        .bb-job-title {
            font-size: 48px;
            text-transform: uppercase;
            font-weight: bold;
            color: #e30613;
            text-shadow: 0 0 6px rgba(0, 0, 0, 0.2), 0 0 12px rgba(0, 0, 0, .1);
            padding: 0 !important;
            margin: 0 !important;
        }
        p.bb-job-title-extra {
            color: #e30613;
            font-size: 28px;
        }

        .bb-job-info {
            width: 80%;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 0rem;
            margin: auto;
            transform: translateY(104px);
        }
        @media screen and (max-width: 1399px) {
            .bb-job-info {
                width: 100%;
            }
        }
        .bb-job-item {
            flex: 1;
            background: rgba(227, 6, 19, 0.8);
            padding: 0.5rem 2rem;
            /* border-radius: 5px; */
            color: white;
            font-weight: bold;
            clip-path: polygon(20% 0%, 100% 0, 80% 100%, 0% 100%);
            min-height: 104px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            margin-right: -1rem;
            margin-left: -1rem;
        }
        .bb-job-item span {
            width: 100%;
            text-align: left;
            margin-left: calc(20% - 1rem);
            display: block;
            font-size: 12px;
            text-transform: uppercase;
            opacity: 1;
        }
        .bb-job-item p {
            margin: 0;
            font-size: 20px;
            padding-right: calc(20% - 1rem);
            hyphens: auto;
            text-align: right;
            padding-bottom: 0;
        }

        .bb-job-item p.extra {
            font-size: 12px;
        }


        @media screen and (max-width: 999px) {
            .bb-job-info {
                display: grid;
                grid-template-columns: 1fr 1fr;
                justify-content: center;
                flex-wrap: wrap;
                margin: auto;
                transform: translateY(0px);
                margin-top: 1rem;
                row-gap: 2rem;
            }

            .bb-job-item {
                margiin-bottom: 2rem;
            }

            .bb-job-title {
                font-size: 24px;
            }
        }

        @media screen and (max-width: 519px) {
            .bb-job-teaser {
                padding: 2rem;
                min-height: calc(100vh - 48px - 74px);
                min-height: calc(100svh - 48px - 74px);
            }

            .bb-job-intro {
                text-shadow: 0 0 6px rgba(0, 0, 0, 0.5), 0 0 12px rgba(0, 0, 0, .5);
            }
            .bb-job-title {
                text-shadow: 0 0 6px rgba(0, 0, 0, 0.5), 0 0 12px rgba(0, 0, 0, .5);
            }
            p.bb-job-title-extra {
                font-size: 18px;
            }

            .bb-job-info {
                margin-top: 1rem;
                row-gap: 1rem;
            }

            .bb-job-item {
                padding: 0.25rem 2rem;
                min-height: 92px;
                margin-right: 0;
                margin-left: 0;
                width: 100%;
            }

            .bb-job-item span {
                font-size: 10px;
                font-weight: 600;
            }

            .bb-job-item p {
                margin: 0;
                font-size: 16px;
                padding-right: calc(20% - 1rem);
                hyphens: auto;
                word-break: break-word;
                text-align: right;
                font-weight: 600;
            }

            .bb-job-item p.extra {
                font-size: 11px;
            }
        }
    </style>
    <?php
    return ob_get_clean();
}
add_shortcode('bb_job_teaser', 'bb_job_teaser_shortcode');

// function bb_job_teaser_assets() {
//     wp_enqueue_style('bb-job-teaser-style', plugin_dir_url(__FILE__) . 'bb-job-teaser.css');
// }
// add_action('wp_enqueue_scripts', 'bb_job_teaser_assets');


/*
vc_map(array(
    'name' => __('Job Teaser', 'bb'),
    'base' => 'bb_job_teaser',
    'category' => __('BB Elemente', 'bb'),
    'icon' => 'icon-wpb-ui-icon',
    'class' => '',
    'params' => array(
        array(
            'type' => 'attach_image',
            'heading' => __('Hintergrundbild', 'bb'),
            'param_name' => 'background_image',
            'description' => __('Bild als Hintergrund festlegen.', 'bb'),
        ),
        array(
            'type' => 'textfield',
            'heading' => __('Intro Text', 'bb'),
            'param_name' => 'intro_text',
        ),
        array(
            'type' => 'textfield',
            'heading' => __('Stellenbezeichnung', 'bb'),
            'param_name' => 'job_title',
        ),
        array(
            'type' => 'textfield',
            'heading' => __('Gehalt', 'bb'),
            'param_name' => 'salary',
        ),
        array(
            'type' => 'textfield',
            'heading' => __('Voraussetzung', 'bb'),
            'param_name' => 'requirement',
        ),
        array(
            'type' => 'textfield',
            'heading' => __('Beginn', 'bb'),
            'param_name' => 'start_date',
        ),
        array(
            'type' => 'textfield',
            'heading' => __('Dauer', 'bb'),
            'param_name' => 'duration',
        ),
    ),
));
*/

vc_map(array(
    'name' => __('Job Teaser', 'bb'),
    'base' => 'bb_job_teaser',
    'category' => __('BB Elemente', 'bb'),
    'icon' => 'icon-wpb-ui-icon',
    'class' => '',
    'params' => array(
        array(
            'type' => 'attach_image',
            'heading' => __('Hintergrundbild', 'bb'),
            'param_name' => 'background_image',
            'description' => __('Bild als Hintergrund festlegen.', 'bb'),
        ),
        array(
            'type' => 'textfield',
            'heading' => __('Intro Text', 'bb'),
            'param_name' => 'intro_text',
        ),
        array(
            'type' => 'textfield',
            'heading' => __('Stellenbezeichnung', 'bb'),
            'param_name' => 'job_title',
        ),
        array(
            'type' => 'textfield',
            'heading' => __('Stellenbezeichnung Zusatz', 'bb'),
            'param_name' => 'job_title_extra',
        ),
        // Gruppe "Gehalt": Hauptzeile und zusätzliche Zeile
        array(
            'type' => 'textfield',
            'heading' => __('Gehalt', 'bb'),
            'param_name' => 'salary',
            'group'    => __('Gehalt', 'bb'),
        ),
        array(
            'type' => 'textfield',
            'heading' => __('Gehalt Zusatz', 'bb'),
            'param_name' => 'salary_extra',
            'group'    => __('Gehalt', 'bb'),
        ),
        // Gruppe "Voraussetzungen": Hauptzeile und zusätzliche Zeile
        array(
            'type' => 'textfield',
            'heading' => __('Voraussetzung', 'bb'),
            'param_name' => 'requirement',
            'group'    => __('Voraussetzungen', 'bb'),
        ),
        array(
            'type' => 'textfield',
            'heading' => __('Voraussetzung Zusatz', 'bb'),
            'param_name' => 'requirement_extra',
            'group'    => __('Voraussetzungen', 'bb'),
        ),
        // Gruppe "Beginn": Hauptzeile und zusätzliche Zeile
        array(
            'type' => 'textfield',
            'heading' => __('Beginn', 'bb'),
            'param_name' => 'start_date',
            'group'    => __('Beginn', 'bb'),
        ),
        array(
            'type' => 'textfield',
            'heading' => __('Beginn Zusatz', 'bb'),
            'param_name' => 'start_date_extra',
            'group'    => __('Beginn', 'bb'),
        ),
        // Gruppe "Dauer": Hauptzeile und zusätzliche Zeile
        array(
            'type' => 'textfield',
            'heading' => __('Dauer', 'bb'),
            'param_name' => 'duration',
            'group'    => __('Dauer', 'bb'),
        ),
        array(
            'type' => 'textfield',
            'heading' => __('Dauer Zusatz', 'bb'),
            'param_name' => 'duration_extra',
            'group'    => __('Dauer', 'bb'),
        ),
    ),
));
