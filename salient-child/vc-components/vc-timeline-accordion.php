<?php
if (!class_exists('WPBakeryShortCode_BB_Timeline_Accordion')) {
    class WPBakeryShortCode_BB_Timeline_Accordion extends WPBakeryShortCode {
    }
}

function bb_timeline_accordion_shortcode($atts, $content = null) {
    $atts = shortcode_atts([
        'steps' => '',
        'line_color' => '#0f2851',
        'text_color' => '#0f2851'
    ], $atts);

    $steps = vc_param_group_parse_atts($atts['steps']);

    ob_start();
    ?>
    <div class="bb-timeline-container" style="position: relative;">
        <?php foreach ($steps as $index => $step) : ?>
            <div class="bb-timeline-step">
                <div class="bb-timeline-icon" style="background: #ffffff;">
                    <i class="<?php echo esc_attr($step['icon']); ?>" style="color: #0f2851;"></i>
                </div>
                <div class="bb-timeline-header" onclick="toggleAccordion(this)">
                    <h3 style="color: <?php echo esc_attr($atts['text_color']); ?>;">
                        <?php echo esc_html($step['title']); ?>
                    </h3>
                    <span class="bb-toggle-icon">+</span>
                </div>
                <div class="bb-timeline-content" style="display: none;">
                    <p style="color: <?php echo esc_attr($atts['text_color']); ?>;">
                        <?php echo esc_html($step['desc']); ?>
                    </p>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
    <script>
        function toggleAccordion(element) {
            var content = element.nextElementSibling;
            if (content.style.display === "none" || content.style.display === "") {
                content.style.display = "block";
                element.querySelector(".bb-toggle-icon").textContent = "-";
            } else {
                content.style.display = "none";
                element.querySelector(".bb-toggle-icon").textContent = "+";
            }
        }
    </script>
    <style>
        .bb-timeline-container i[class*=fa-], .bb-timeline-container span[class*=fa-] {
            font-size: 24px;
        }
        body .row .bb-timeline-container h3 {
            user-select: none;
            padding: 0;
            padding-bottom: 0 !important;
            margin: 0;
            margin-bottom: 0 !important;
        }

        .bb-timeline-container::after {
            content: "";
            position: absolute;
            top: 40px;
            bottom: 0;
            left: 30px;
            width: 4px;
            background: <?php echo esc_attr($atts['line_color']); ?>;
        }
        .bb-timeline-step {
            margin-bottom: 20px;
            padding: 10px 15px;
            position: relative;
        }
        .bb-timeline-icon {
            border: solid #0f2851 3px;
            width: 64px;
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            position: absolute;
            left: 0;
            top: 0;
            z-index: 1;
        }
        .bb-timeline-header {
            display: flex;
            align-items: center;
            cursor: pointer;
            padding: 0;
            font-weight: bold;
            background: none;
            position: relative;
            margin-left: 60px;
        }
        .bb-timeline-header h3 {
            font-size: 18px;
        }
        .bb-timeline-content {
            padding: 0;
            background: white;
            border-radius: 5px;
            margin-top: 5px;
            margin-left: 60px;
        }
        .bb-toggle-icon {
            font-size: 24px;
            color: #0f2851;
            margin-left: auto;
        }

        @media screen and (max-width: 690px) {
            body .row .bb-timeline-container h3 {
                font-size: 18px;
                line-height: 24px;
                padding: 9px 0px 9px 0 !important;
            }
        }
    </style>
    <?php
    return ob_get_clean();
}
add_shortcode('bb_timeline_accordion', 'bb_timeline_accordion_shortcode');

function bb_timeline_accordion_assets() {
    wp_enqueue_style('bb-timeline-style', plugin_dir_url(__FILE__) . 'bb-timeline.css');
    wp_enqueue_script('bb-timeline-script', plugin_dir_url(__FILE__) . 'bb-timeline.js', array('jquery'), null, true);
}
add_action('wp_enqueue_scripts', 'bb_timeline_accordion_assets');

vc_map(array(
    'name' => __('Timeline mit Accordion', 'bb'),
    'base' => 'bb_timeline_accordion',
    'category' => __('BB Elemente', 'bb'),
    'icon' => 'icon-wpb-ui-accordion',
    'class' => '',
    'params' => array(
        array(
            'type' => 'colorpicker',
            'heading' => __('Linienfarbe', 'bb'),
            'param_name' => 'line_color',
            'description' => __('Farbe der Timeline-Linie und Icons.', 'bb'),
        ),
        array(
            'type' => 'colorpicker',
            'heading' => __('Textfarbe', 'bb'),
            'param_name' => 'text_color',
            'description' => __('Farbe der Titel und Beschreibungen.', 'bb'),
        ),
        array(
            'type' => 'param_group',
            'heading' => __('Timeline Steps', 'bb'),
            'param_name' => 'steps',
            'params' => array(
                array(
                    'type' => 'iconpicker',
                    'heading' => __('Icon', 'bb'),
                    'param_name' => 'icon',
                    'description' => __('Wähle ein Icon für diesen Schritt.', 'bb'),
                ),
                array(
                    'type' => 'textfield',
                    'heading' => __('Titel', 'bb'),
                    'param_name' => 'title',
                    'description' => __('Gib den Titel des Schrittes ein.', 'bb'),
                ),
                array(
                    'type' => 'textarea',
                    'heading' => __('Beschreibung', 'bb'),
                    'param_name' => 'desc',
                    'description' => __('Gib eine Beschreibung für den Schritt ein.', 'bb'),
                ),
            ),
        ),
    ),
));
