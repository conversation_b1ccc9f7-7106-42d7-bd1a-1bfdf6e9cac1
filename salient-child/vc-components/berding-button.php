<?php
// <PERSON><PERSON><PERSON><PERSON>, dass WPBakery Page Builder aktiv ist
if ( ! defined( 'ABSPATH' ) || ! function_exists( 'vc_map' ) ) {
    return;
}

// Registrierung des Custom WPBakery Moduls für den Button
function custom_vc_button() {
    vc_map( array(
        'name'        => __( 'Custom Button', 'my-text-domain' ),
        'base'        => 'custom_vc_button',
        'category'    => __( 'Custom Elements', 'my-text-domain' ),
        'description' => __( 'Ein stilisierter Button', 'my-text-domain' ),
        'params'      => array(
            array(
                'type'        => 'vc_link',
                'heading'     => __( 'Button URL', 'my-text-domain' ),
                'param_name'  => 'url',
            ),
            array(
                'type'        => 'dropdown',
                'heading'     => __( 'Button Style', 'my-text-domain' ),
                'param_name'  => 'button_style',
                'value'       => array(
                    __( 'Light', 'my-text-domain' ) => 'light',
                    __( 'Dark', 'my-text-domain' )  => 'dark',
                ),
                'std'         => 'dark',
            ),
            array(
                'type' => 'checkbox',
                'heading' => 'Fullwidth',
                'param_name' => 'full_width_checkbox',
                'value' => array('Ja' => 'true'),
                'description' => 'Zeige die Google Reviews über der Überschrift an.',
            ),

            array(
                'type'        => 'dropdown',
                'heading'     => __( 'Button Größe', 'my-text-domain' ),
                'param_name'  => 'size',
                'value'       => array(
                    __( 'Klein', 'my-text-domain' )  => 'small',
                    __( 'Mittel', 'my-text-domain' ) => 'medium',
                    __( 'Groß', 'my-text-domain' )   => 'large',
                ),
                'std'         => 'medium',
            ),
            // array(
            //     'type'        => 'dropdown',
            //     'heading'     => __( 'Fullwidth', 'my-text-domain' ),
            //     'param_name'  => 'full_width',
            //     'value'       => array(
            //         __( 'Ja', 'my-text-domain' )  => 'ja',
            //         __( 'Nein', 'my-text-domain' ) => 'nein',
            //     ),
            //     'std'         => 'medium',
            // ),
        
            array(
                'type'        => 'textfield',
                'heading'     => __( 'Custom CSS Klasse', 'my-text-domain' ),
                'param_name'  => 'class',
            )
        ),
    ));
}
add_action( 'vc_before_init', 'custom_vc_button' );

// Shortcode-Funktion zur Darstellung des Buttons
function render_custom_vc_button( $atts ) {
    $atts = shortcode_atts(
        array(
            'url'          => '',
            'button_style' => 'dark',
            'size'         => 'medium',
            'full_width_checkbox'   => '',
            'class'        => '',
        ),
        $atts
    );

    $link = vc_build_link( $atts['url'] );
    $href = isset( $link['url'] ) ? esc_url( $link['url'] ) : '#';
    $target = isset( $link['target'] ) ? esc_attr( $link['target'] ) : '_self';
    $text = isset( $link['title'] ) ? esc_html( $link['title'] ) : 'Button';

    $styles = [
        'light' => '--default-color: #ffffff; --text-color: #0f2851; --hover-color: #ea0000; --hover-text-color: #ffffff;',
        'dark'  => '--default-color: #0f2851; --text-color: #ffffff; --hover-color: #ea0000; --hover-text-color: #ffffff;',
    ];

    $button_classes = "custom-vc-button {$atts['size']} {$atts['class']} {$atts['button_style']}";
    if ( $atts['full_width_checkbox'] === 'true' ) {
        $button_classes .= ' full-width';
    }
    $style = $styles[$atts['button_style']];

    return '<a href="' . $href . '" target="' . $target . '" class="' . esc_attr( $button_classes ) . '" style="' . esc_attr( $style ) . '">' . $text . '</a>';
}
add_shortcode( 'custom_vc_button', 'render_custom_vc_button' );

// Stile für den Button
function custom_vc_button_styles() {
    echo '<style>
        .custom-vc-button {
            display: inline-block;
            padding: 12px 40px;
            border-radius: 0;
            clip-path: polygon(50% 0%, 100% 0, 95% 50%, 90% 100%, 75% 100%, 50% 100%, 15% 100%, 0 100%, 5% 50%, 10% 0);
            text-decoration: none;
            font-weight: 600;
            background-color: var(--default-color);
            color: var(--text-color);
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        .custom-vc-button:hover {
            background-color: var(--hover-color);
            color: var(--hover-text-color);
        }
        .custom-vc-button.small {
            font-size: 12px;
            padding: 5px 10px;
        }
        .custom-vc-button.medium {
            font-size: 16px;
        }
        .custom-vc-button.large {
            font-size: 20px;
            padding: 15px 30px;
        }

        .custom-vc-button.full-width {
            display: block;
            width: 100%;
            text-align: center;
        }
    </style>';
}
add_action( 'wp_head', 'custom_vc_button_styles' );

/*
// Registrierung des Custom WPBakery Moduls für den Button
function custom_vc_button() {
    vc_map( array(
        'name'        => __( 'Custom Button', 'my-text-domain' ),
        'base'        => 'custom_vc_button',
        'category'    => __( 'Custom Elements', 'my-text-domain' ),
        'description' => __( 'Ein stilisierter Button', 'my-text-domain' ),
        'params'      => array(
            array(
                'type'        => 'textfield',
                'heading'     => __( 'Button Text', 'my-text-domain' ),
                'param_name'  => 'text',
                'value'       => __( 'Click Me', 'my-text-domain' ),
                'admin_label' => true,
            ),
            array(
                'type'        => 'vc_link',
                'heading'     => __( 'Button URL', 'my-text-domain' ),
                'param_name'  => 'url',
            ),
            array(
                'type'        => 'colorpicker',
                'heading'     => __( 'Button Farbe', 'my-text-domain' ),
                'param_name'  => 'color',
                'value'       => '#0073e6',
            ),
            array(
                'type'        => 'colorpicker',
                'heading'     => __( 'Hover Farbe', 'my-text-domain' ),
                'param_name'  => 'hover_color',
                'value'       => '#005bb5',
            ),
            array(
                'type'        => 'dropdown',
                'heading'     => __( 'Button Größe', 'my-text-domain' ),
                'param_name'  => 'size',
                'value'       => array(
                    __( 'Klein', 'my-text-domain' )  => 'small',
                    __( 'Mittel', 'my-text-domain' ) => 'medium',
                    __( 'Groß', 'my-text-domain' )   => 'large',
                ),
                'std'         => 'medium',
            ),
            array(
                'type'        => 'textfield',
                'heading'     => __( 'Custom CSS Klasse', 'my-text-domain' ),
                'param_name'  => 'class',
            ),
        ),
    ));
}
add_action( 'vc_before_init', 'custom_vc_button' );

// Shortcode-Funktion zur Darstellung des Buttons
function render_custom_vc_button( $atts ) {
    $atts = shortcode_atts(
        array(
            'text'        => 'Click Me',
            'url'         => '',
            'color'       => '#0073e6',
            'hover_color' => '#005bb5',
            'size'        => 'medium',
            'class'       => '',
        ),
        $atts
    );

    $link = vc_build_link( $atts['url'] );
    $href = isset( $link['url'] ) ? esc_url( $link['url'] ) : '#';
    $target = isset( $link['target'] ) ? esc_attr( $link['target'] ) : '_self';

    $button_classes = "custom-vc-button {$atts['size']} {$atts['class']}";
    $style = "--default-color: {$atts['color']}; --hover-color: {$atts['hover_color']};";

    return '<a href="' . $href . '" target="' . $target . '" class="' . esc_attr( $button_classes ) . '" style="' . esc_attr( $style ) . '">' . esc_html( $atts['text'] ) . '</a>';
}
add_shortcode( 'custom_vc_button', 'render_custom_vc_button' );

// Stile für den Button
function custom_vc_button_styles() {
    echo '<style>
        .custom-vc-button {
            display: inline-block;
            padding: 12px 40px;
            border-radius: 0;
            color: #fff;
            text-decoration: none;
            font-weight: bold;
            transition: 0.3s;
            clip-path: polygon(50% 0%, 100% 0, 95% 50%, 90% 100%, 75% 100%, 50% 100%, 15% 100%, 0 100%, 5% 50%, 10% 0);
            background-color: var(--default-color);
        }
        .custom-vc-button:hover {
            color: #fff;
            background-color: var(--hover-color);
        }
        .custom-vc-button.small {
            font-size: 12px;
            padding: 5px 10px;
        }
        .custom-vc-button.medium {
            font-size: 16px;
        }
        .custom-vc-button.large {
            font-size: 20px;
            padding: 15px 30px;
        }
    </style>';
}
add_action( 'wp_head', 'custom_vc_button_styles' );
*/