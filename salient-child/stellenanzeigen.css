@font-face {
    font-family: 'Material Symbols Outlined';
    font-style: normal;
    font-weight: 100 700;
    src: url(/wp-content/themes/salient-child/materialicons.woff2) format('woff2');
}
.material-symbols-outlined {
    font-family: 'Material Symbols Outlined';
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    -webkit-font-feature-settings: 'liga';
    -webkit-font-smoothing: antialiased;
}
.material-symbols-outlined {
    font-variation-settings:
    'FILL' 0,
    'wght' 400,
    'GRAD' 0,
    'opsz' 48
}

/* just Hide It */
.hide-it {
    display: none;
}

/************ */
/* JOB FILTER */ 
/************ */

.softgarden-controls.background {
    background: #162751;
    padding: 30px 0 30px 0px;
    margin-bottom: 48px;
}
.main-search {
    background: rgba(255,255,255,0.9);
    border-radius: 4px;
    padding: 10px 20px;
}

.top-row {
    display: flex;
    justify-content: space-between;
}

.search-wrapper {
    width: 45%;
}
/* Search Inputs */ 
.search-wrapper .searchinput {
    height: 44px;
    width: 100%;
    padding: 10px 10px;
    display: inline-block;
    position: relative;
    outline: 0px;
    border: 2px solid #7a879f;
    border-radius: 4px 4px 4px 4px;
}
input.searchinput {
    font-size: 18px;
}

/* DEINE ADRESSE / yourAdress – show and hide */ 
.search-wrapper.adress .yourAdress {
    display: none;
}
.search-wrapper.adress:focus-within .yourAdress {
    display: block;
}

/* Zeige Hinweis "Deine aktuellr Standort" */ 
span.hinweis-deine-adresse {
    font-size: 18px;
    position: absolute;
    z-index: 5;
    padding: 6px 8px;
    margin-top: 2px;
    margin-left: 2px;
    pointer-events: none;
    background: #fff;
}

/* Buttons */ 
.main-search button {
    margin-top: 0px;
    font-size: 18px;
    padding: 8px 16px;
    text-align: center;
    width: 100px;
}

/* Search Buttons */ 
.softgarden-modul button.search {
    padding: 10px 0px;
    width: 48px;
    height: 44px;
}

.softgarden-modul button.search:hover {
    /* New Hover */ 
    background: #e30613 !Important;
    color: #f0f0f1;
}

/* TEXTSUCHE */ 
.adress-wrapper {
    position: relative;
}

.adress-wrapper .yourAdress {
    display: none;
}

.adress-wrapper:hover .yourAdress , .adress-wrapper:active .yourAdress , .adress-wrapper:focus .yourAdress {
    display: block;
}


/* Deine Adresse Nutzen */ 
body[data-button-style*="slightly_rounded"] button.yourAdress {
    position: absolute;
    z-index: 10;
    margin-left: 0;
    /* width: calc(45% - 16px); */
    width: calc(100% - 96px);
    margin-top: 44px;
    padding: 0px !important;
    height: 44px;
    background: #fff !important;
    color: #162751 !important;
    border: solid 2px;
    border-radius: 0px 0px 4px 4px !important;
    text-align: left;
    padding: 10px 10px !important;
    display: inline-block;
    font-size: 18px !important;
}

/* Distance */
/*
.distance-wrapper {
    display: inline-block;
    height: 38px;
    background: #73b740;
    padding: 0px !important;
}
*/
.distance-wrapper {
    display: inline-grid;
    margin-left: -114px;
    height: 44px;
    /* background: #73b740; */
    padding: 0px !important;
    /* overflow: hidden; */
    /* display: inline-block; */
    position: relative;
    outline: 0px;
    border: 2px solid #162751;
    border-radius: 0px 4px 4px 0px;
    z-index: 99;
    /* display: none; */
}

.change-distance {
    display: block;
    position: absolute;
    border-radius: 4px;
    overflow: hidden;
    z-index: 9;
    border: solid 2px #162751;
    left: -2px;
    top: -2px;
}



body .softgarden-modul .distance-wrapper button, body[data-button-style*="slightly_rounded"] .softgarden-modul .distance-wrapper button {
    margin-right: 0px !important;
    border-radius: 0px !important;
    -webkit-border-radius: 0px !important;
    border: none !important;
    height: 40px;
    line-height: 20px;
    padding-right: 14px;
    padding-left: 0px;
    width: 130px;
}

.distance-wrapper button .material-symbols-outlined {
    font-variation-settings: 'FILL' 0, 'wght' 400, 'GRAD' 0, 'opsz' 48;
    color: #fff;
    z-index: 9;
    position: absolute;
}


body .softgarden-modul .distance-wrapper .change-distance button:first-child, body[data-button-style*="slightly_rounded"] .softgarden-modul .distance-wrapper .change-distance button:first-child {
    margin-top: -2px;
}

body .softgarden-modul .distance-wrapper .change-distance button, body[data-button-style*="slightly_rounded"] .softgarden-modul .distance-wrapper .change-distance button {
    height: 42px;
    background: #fff !important;
    color: #162751;
    border-top: solid 2px #162751 !important;
}

body .softgarden-modul .distance-wrapper .change-distance button:hover, body[data-button-style*="slightly_rounded"] .softgarden-modul .distance-wrapper .change-distance button:hover {
    background: #162751 !important;
    color: #fff;
    border-top: solid 2px #162751 !important;
}



.distance-wrapper button {
    display: block !important;
    position: relative;
}

/* Hover und Active */

.distance-wrapper button:hover .material-symbols-outlined, .distance-wrapper button.active .material-symbols-outlined {
    color: #162751;
}

.distance-wrapper button.active {
    background: #fff !Important;
    color: #162751;
}



/* BOTTOM ROW */ 
.bottom-row {
    margin-top: 0px;
}

.category-search button {
    font-size: 18px;
    
}

.category-search button.active {
    background: #fff !important;
    color: #162751;
}




/* Single BAR with Explenations */ 
.softgarden-modul .job-infos-bar.job-header {
    background: #fff !important;
    padding: 10px 20px !important;
}
.softgarden-modul .job-infos-bar.job-header:hover div {
    color: #162751 !important;
}

/************* */
/* SINGLE JOBS */ 
/************* */

/* SOFTGARDEN JOBS CARD STYLE */ 
.softgarden-modul .job-header {
    display: flex;
    justify-content: inherit;
    border-bottom: 2px solid #162751;
    padding: 0px !important;
    transition: color 150ms ease, background 150ms ease;
}
/* Job header hover */ 
.softgarden-modul .job-header:hover {
    background: #162751;
    color: #fff !important;
}
.softgarden-modul .job-header:hover p, .softgarden-modul .job-header:hover h3 {
    color: #fff !important;
}

.job-header .category {
    width: 100%;
}

.softgarden-modul .job-header:hover .job-btn-wrapper a.btn {
    border: solid 2px #fff;
    padding: 10px 16px 10px 16px;
    color: #fff !important;
}


a.left-job-flex {
    width: 100%;
    padding: 10px 20px;
    display: flex;
}

.softgarden-modul ul.jobs.card .job-header div p, .softgarden-modul ul.jobs.card .job-header div h3 {
    display: block;
    line-height: 28px;
    font-size: 18px;
    padding: 0px;
}
.job-header .left50 {
    width: 70% !important;
    margin-right: 0;
}

.softgarden-modul ul.jobs.card li .left50 p {
    color: #676767;
    font-weight: 400;
}
.softgarden-modul ul.jobs.card li .left50 h3 {
    color: #0f2851;
    font-size: 18px;
    font-weight: 700;
    display: inline-block;
    width: 75%;
}

.job-header .left50 label {
    margin-top: 2px;
    margin-left: -4px;
    float: right;
    margin-left: auto;
    margin-right: 20%;
    cursor:s-resize;
}
.job-header  .left50 label .material-symbols-outlined {
    color: #0f2851;
    transition: color 150ms ease;
}
.job-header:hover  .left50 label .material-symbols-outlined {
    color: #fff;
}

/* Job btn */ 
.job-btn-wrapper {
    position: absolute;
    width: 20%;
    margin-left: 80%;
    padding: 14px 20px;
}
.job-btn-wrapper .inner-wrapper {
    position: relative;
}
.job-btn-wrapper .inner-wrapper:before {
    content: '';
    position: absolute;
    width: 0%;
    height: 52px;
    background: #fff;
    transition: all 300ms ease;
}
.job-btn-wrapper .inner-wrapper:hover:before {
    content: '';
    position: absolute;
    width: 100%;
    height: 52px;
    background: #fff;
}
.softgarden-modul .job-btn-wrapper .inner-wrapper a.btn {
    width: 100%;
    transition: all 300ms ease;
}
.softgarden-modul .job-btn-wrapper .inner-wrapper:hover a.btn {
    color: #0f2851 !important;
}

/* Location Icons */ 
.location span.material-symbols-outlined {
    position: absolute;
    margin-left: -30px;
    margin-top: 2px;
}

/* Description */ 
/*
input.chck1:checked + .description {
    display: block;
    margin-top: -4px;
    padding: 2px 20px;
}
input.chck1:checked + .description {
    display: block;
    margin-top: -4px;
    padding: 2px 20px 30px 20px;
    border-bottom: 2px solid;
}
.description ul li {
    background: none !important;
}
*/

/******** */
/* MOBILE */ 
/******** */
@media screen and (max-width: 999px) {
    .softgarden-modul ul.jobs li .job-header {
        display: block !important;
    }

    .job-header .left50 label {
        display: none !important;
    }

    a.left-job-flex {
        width: 100%  !important;
        padding: 10px 20px  !important;
        display: block !important;
    }
    .job-header .location {
        width: 100% !important;
        padding-left: 30px !important;
    }

    .job-btn-wrapper {
        position: relative;
        width: 100% !important;
        margin-left: 0 !important;
        padding: 14px 20px !important;
    }


}


/*************************/
/* Softgarden API MODULE */ 
.softgarden-modul .content-width {
    display: block;
    position: relative;
}


/* Actions (Buttons, Search etc.) */ 
.softgarden-content button, .softgarden-content input {
    margin-top: 10px;
    padding: 8px 18px;
    font-size: 18px;
    outline: none;
    border: solid 2px #162751;
}

/* Buttons */ 
.softgarden-modul button {
    background: #162751 !Important;
    margin-right: 6px;
}
.softgarden-modul button:hover {
    background: #fff !Important;
    color: #162751;
}

/* Search */ 
.softgarden-content input.search {
    border-radius: 4px 0px 0px 4px !important;
}
.softgarden-content button.search {
    border-radius: 0px 4px 4px 0px !important;
}


/*******/
/* BAR */ 
/*******/ 

.softgarden-modul .job-header {
    display: flex;
    justify-content: inherit;
    border-bottom: 2px solid #162751;
    padding: 20px 20px 20px 20px;
}


/*****************/ 
/* No Jobs found */
/*****************/

.softgarden-modul ul.jobs .single-job-wrapper.no-job {
    width: 100%;
    border: none !important;
}
.softgarden-modul ul.jobs .no-job .job-header {
    border: none;
}
.single-job-wrapper.no-job .left50 {
    width: 100% !important;
    border: none !important;
    padding: 0px 0px 0px 0px;
}


/***************/ 
/* Actual Jobs */
/***************/ 

/* Hintergrund jedes zweiten Elements */ 
.softgarden-modul ul.jobs li:nth-child(odd) {
    background: #f1f2f5;
}

.softgarden-modul ul.jobs li .job-header {
    display: flex;
    justify-content: space-between;
    border-bottom: 2px solid #162751;
    padding: 20px 20px 20px 20px;
}

label.job-header div {
    flex: 0 1 auto;
}


/* Job Bezeichnung */ 
.job-header .left50 {
    width: 44%;
    margin-right: 0;
}


.job-header .category {
    width: 10%;
}
.job-header .location {
    width: 10%;
}
.job-header .distance {
    width: 10%;
}

.single-job-wrapper a.btn, .job-header .button-space {
    /* width: 16%; */ 
    width: 16%;
    text-align: center;
}
.single-job-wrapper a.btn {
    line-height: 28px;
    position: relative;
    max-height: 54px;
    display: block;
    clip-path: none;
    background: none;
    border: solid 2px #0f2851;
    padding: 10px 16px 10px 16px;
    color: #0f2851 !important;
}
.single-job-wrapper a.btn:hover {
    clip-path: none;
    background: none;
    border: solid 2px #0f2851;
    color: #0f2851 !important;
}

/* Schrift und Line Height */ 
.softgarden-modul .job-header div a {
        position: relative;
}
.softgarden-modul .job-header div p,
.softgarden-modul .job-header div a p {
    padding: 14px 0px;
    line-height: 26px;
    display: inline-block;
}
@media screen and (min-width: 1400px) {
    .softgarden-modul .job-header div a p::before {
        content: '';
        position: absolute;
        display: block;
        width: 100%;
        height: 2px;
        top: 20px;
        bottom: 0;
        left: 0;
        background-color: #0e2851;
        transform: scaleX(0%);
        transform-origin: top left;
        transition: transform 0.3s ease;
    }
    .softgarden-modul ul.jobs li .job-header div a:hover p::before {
        transform: scaleX(100%);
        transition: transform 0.3s ease;
    }
}

.softgarden-modul ul.jobs li .left50 p {
    color: #0f2851;
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 4px;
}
.job-header a.btn {
    line-height: 30px;
}


ul.jobs {
    margin-left: 0px;
    margin-bottom: 30px;
}

.softgarden-modul ul.jobs li {
    list-style: none;
}

.softgarden-modul ul.jobs li ul li {
    margin-bottom: 0px;
}
.softgarden-modul .jobs input {
    display: none;
}


.softgarden-modul .description.hide {
    display: none;
}
.softgarden-modul .description .html-import {
    font-size: 18px;
}
.softgarden-modul ul.jobs .html-import ul {
    margin-bottom: 30px !important;
    margin-top: 12px;
}

.softgarden-modul ul.jobs .html-import ul li {
    list-style: disc;
}
.softgarden-modul .description p {
    margin-top: 20px;
    margin-bottom: 0px;
}
/*
input.chck1:checked + .description {
    display: block;
}
*/



/* Loading */ 
.job-loader {
    text-align: center;
    padding: 20vh 0 50vh 0;
}
.lds-ring {
    display: inline-block;
    position: relative;
    width: 80px;
    height: 80px;
}
.lds-ring div {
    box-sizing: border-box;
    display: block;
    position: absolute;
    width: 64px;
    height: 64px;
    margin: 8px;
    border: 8px solid #162751;
    border-radius: 50%;
    animation: lds-ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
    border-color: #162751 transparent transparent transparent;
}
.lds-ring div:nth-child(1) {
    animation-delay: -0.45s;
}
.lds-ring div:nth-child(2) {
    animation-delay: -0.3s;
}
.lds-ring div:nth-child(3) {
    animation-delay: -0.15s;
}
@keyframes lds-ring {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}



@media screen and (max-width: 999px) {
    .main-search {
        background: rgba(255,255,255,0.9);
        border-radius: 4px;
        padding: 10px 20px;
        margin: 0px 30px;
    }

    .top-row {
        display: block;
        justify-content: space-between;
    }

    .search-wrapper {
        width: 100%;
        margin-bottom: 12px;
    }

    body[data-button-style*="slightly_rounded"] button.yourAdress {
        width: auto;
        margin-top: 42px;
        height: 44px;
        font-size: 16px !important;
    }

    /* Distance */ 
    .distance-wrapper {
        display: inline-grid;
        margin-left: 182px;
        height: 43px;
        /* background: #73b740; */
        padding: 0px !important;
        /* overflow: hidden; */
        /* display: inline-block; */
        position: relative;
        outline: 0px;
        border: 2px solid #162751;
        border-radius: 0px 4px 4px 0px;
        z-index: 99;
        display: none;
        position: absolute;
        margin-top: -55px;
    }

    .softgarden-modul button.search {
        padding: 10px 0px;
        width: 100%;
        height: 44px;
    }

    /* Category Search */ 
    .category-search {
        margin: 0px 30px;
    }

    .category-search button {
        font-size: 16px;
        padding: 10px;
    }

    
}